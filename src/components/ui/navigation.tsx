'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>, Translate } from '@/components/ui';
import { cn } from '@/lib/utils';
import { ArrowLeft, BarChart3, BookOpen, Home, Languages, Menu, X, Search } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React from 'react';

interface NavigationItem {
	href: string;
	labelKey: string;
	icon: React.ComponentType<{ className?: string }>;
	badge?: string;
	description?: string;
}

const navigationItems: NavigationItem[] = [
	{
		href: '/',
		labelKey: 'nav.home',
		icon: Home,
		description: 'Dashboard overview',
	},
	{
		href: '/collections',
		labelKey: 'nav.collections',
		icon: BookOpen,
		description: 'Manage vocabulary collections',
	},
	{
		href: '/vocabulary-lookup',
		labelKey: 'nav.vocabulary_lookup',
		icon: Search,
		description: 'Search words and explore WordNet data',
	},
	{
		href: '/vocabulary-translate',
		labelKey: 'nav.vocabulary_translate',
		icon: Languages,
		description: 'Translate and get detailed meanings of vocabulary words',
	},
	{
		href: '/dashboard/token-monitoring',
		labelKey: 'nav.token_monitor',
		icon: BarChart3,
		badge: 'New',
		description: 'API usage and optimization insights',
	},
];

interface NavigationProps {
	className?: string;
	variant?: 'sidebar' | 'header' | 'breadcrumb';
	showLabels?: boolean;
}

export function Navigation({ className, variant = 'header', showLabels = true }: NavigationProps) {
	const pathname = usePathname();
	const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);

	const isActive = (href: string) => {
		if (href === '/') {
			return pathname === '/';
		}
		return pathname.startsWith(href);
	};

	if (variant === 'breadcrumb') {
		return (
			<nav className={cn('flex items-center space-x-2 text-sm', className)}>
				<Link
					href="/"
					className="flex items-center text-muted-foreground hover:text-foreground transition-colors"
				>
					<Home className="h-4 w-4 mr-1" />
					<Translate text="nav.home" />
				</Link>
				{pathname !== '/' && (
					<>
						<span className="text-muted-foreground">/</span>
						{navigationItems
							.filter((item) => item.href !== '/' && pathname.startsWith(item.href))
							.map((item, index) => (
								<React.Fragment key={item.href}>
									{index > 0 && <span className="text-muted-foreground">/</span>}
									<Link
										href={item.href}
										className={cn(
											'flex items-center transition-colors',
											isActive(item.href)
												? 'text-foreground font-medium'
												: 'text-muted-foreground hover:text-foreground'
										)}
									>
										<item.icon className="h-4 w-4 mr-1" />
										<Translate text={item.labelKey} />
										{item.badge && (
											<Badge variant="secondary" className="ml-1 text-xs">
												{item.badge}
											</Badge>
										)}
									</Link>
								</React.Fragment>
							))}
					</>
				)}
			</nav>
		);
	}

	if (variant === 'sidebar') {
		return (
			<nav className={cn('space-y-2', className)}>
				{navigationItems.map((item) => (
					<Link key={item.href} href={item.href}>
						<Button
							variant={isActive(item.href) ? 'default' : 'ghost'}
							className={cn(
								'w-full justify-start',
								isActive(item.href) && 'bg-primary text-primary-foreground'
							)}
						>
							<item.icon className="h-4 w-4 mr-2" />
							{showLabels && <Translate text={item.labelKey} />}
							{item.badge && (
								<Badge variant="secondary" className="ml-auto text-xs">
									{item.badge}
								</Badge>
							)}
						</Button>
					</Link>
				))}
			</nav>
		);
	}

	// Header variant (default)
	return (
		<>
			{/* Desktop Navigation */}
			<nav className={cn('hidden md:flex items-center space-x-1', className)}>
				{navigationItems.map((item) => (
					<Link key={item.href} href={item.href}>
						<Button
							variant={isActive(item.href) ? 'default' : 'ghost'}
							size="sm"
							className={cn(
								'flex items-center',
								isActive(item.href) && 'bg-primary text-primary-foreground'
							)}
						>
							<item.icon className="h-4 w-4 mr-2" />
							{showLabels && <Translate text={item.labelKey} />}
							{item.badge && (
								<Badge variant="secondary" className="ml-2 text-xs">
									{item.badge}
								</Badge>
							)}
						</Button>
					</Link>
				))}
			</nav>

			{/* Mobile Navigation */}
			<div className="md:hidden">
				<Button
					variant="ghost"
					size="sm"
					onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
					className="p-2"
				>
					{isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
				</Button>

				{isMobileMenuOpen && (
					<div className="absolute top-full left-0 right-0 bg-background border-b shadow-lg z-50">
						<nav className="container mx-auto py-4 space-y-2">
							{navigationItems.map((item) => (
								<Link
									key={item.href}
									href={item.href}
									onClick={() => setIsMobileMenuOpen(false)}
								>
									<Button
										variant={isActive(item.href) ? 'default' : 'ghost'}
										className={cn(
											'w-full justify-start',
											isActive(item.href) &&
												'bg-primary text-primary-foreground'
										)}
									>
										<item.icon className="h-4 w-4 mr-2" />
										<Translate text={item.labelKey} />
										{item.badge && (
											<Badge variant="secondary" className="ml-auto text-xs">
												{item.badge}
											</Badge>
										)}
									</Button>
								</Link>
							))}
						</nav>
					</div>
				)}
			</div>
		</>
	);
}

interface BackNavigationProps {
	href?: string;
	label?: string;
	className?: string;
}

export function BackNavigation({ href = '/', label = 'Back', className }: BackNavigationProps) {
	return (
		<Link href={href}>
			<Button variant="ghost" size="sm" className={cn('mb-4', className)}>
				<ArrowLeft className="h-4 w-4 mr-2" />
				{label}
			</Button>
		</Link>
	);
}

interface PageHeaderProps {
	title: string;
	description?: string;
	backHref?: string;
	backLabel?: string;
	children?: React.ReactNode;
	className?: string;
}

export function PageHeader({
	title,
	description,
	backHref,
	backLabel = 'Back',
	children,
	className,
}: PageHeaderProps) {
	return (
		<div className={cn('space-y-4', className)}>
			{backHref && <BackNavigation href={backHref} label={backLabel} />}

			<div className="flex items-center justify-between">
				<div className="space-y-1">
					<h1 className="text-2xl font-bold tracking-tight">{title}</h1>
					{description && <p className="text-muted-foreground">{description}</p>}
				</div>
				{children && <div className="flex items-center space-x-2">{children}</div>}
			</div>
		</div>
	);
}

// Quick navigation component for dashboard
export function DashboardNavigation() {
	const pathname = usePathname();

	const dashboardItems: NavigationItem[] = [
		{
			href: '/dashboard/token-monitoring',
			labelKey: 'nav.token_monitor',
			icon: BarChart3,
			description: 'API usage insights',
		},
	];

	return (
		<div className="border-b">
			<div className="container mx-auto">
				<nav className="flex items-center space-x-1 py-2">
					{dashboardItems.map((item) => (
						<Link key={item.href} href={item.href}>
							<Button
								variant={pathname === item.href ? 'default' : 'ghost'}
								size="sm"
								className="flex items-center"
							>
								<item.icon className="h-4 w-4 mr-2" />
								<Translate text={item.labelKey} />
							</Button>
						</Link>
					))}
				</nav>
			</div>
		</div>
	);
}
