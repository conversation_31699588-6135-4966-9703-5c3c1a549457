'use client';

import { VocabularyInputForm } from '@/components/vocabulary-translate/vocabulary-input-form';
import { WordList } from '@/app/collections/[id]/vocabulary/generate/components/word-list';
import { <PERSON><PERSON>, Card, ScreenReaderAnnouncement, Translate } from '@/components/ui';
import { useTranslation, useAuth, useGuidance } from '@/contexts';
import { useToast } from '@/contexts/toast-context';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { ArrowLeft, Languages } from 'lucide-react';
import Link from 'next/link';
import { useCallback, useEffect, useState } from 'react';

interface LoadingState {
	[term: string]: {
		gettingDetail: boolean;
		adding: boolean;
	};
}

export default function VocabularyTranslateClient() {
	const { t } = useTranslation();
	const { user } = useAuth();
	const { showError, showSuccess } = useToast();
	const { showGuidance } = useGuidance();
	const [translatedWords, setTranslatedWords] = useState<WordDetail[]>([]);
	const [isTranslating, setIsTranslating] = useState(false);
	const [loadingStates] = useState<LoadingState>({});

	// Set up guidance for this page
	useEffect(() => {
		showGuidance({
			titleKey: 'vocabulary_translate.guidance.title',
			steps: [
				{ key: 'vocabulary_translate.guidance.step1' },
				{ key: 'vocabulary_translate.guidance.step2' },
				{ key: 'vocabulary_translate.guidance.step3' },
			],
			tipKey: 'vocabulary_translate.guidance.tip',
			defaultOpen: true,
		});
	}, [showGuidance]);

	const handleTranslate = useCallback(
		async (words: string[]) => {
			if (!user) {
				showError(new Error(t('errors.authentication_required')));
				return;
			}

			setIsTranslating(true);
			try {
				const response = await fetch('/api/vocabulary-translate', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						words,
						source_language: Language.EN, // Default to English as source
						target_language: Language.VI, // Default to Vietnamese as target
					}),
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(
						errorData.error || t('vocabulary_translate.translation_failed')
					);
				}

				const result: WordDetail[] = await response.json();
				setTranslatedWords(result);
				showSuccess(
					t('vocabulary_translate.translation_success', { count: result.length })
				);
			} catch (error) {
				const err =
					error instanceof Error
						? error
						: new Error(t('vocabulary_translate.translation_failed'));
				showError(err);
			} finally {
				setIsTranslating(false);
			}
		},
		[user, showError, showSuccess, t]
	);

	const getLoadingState = useCallback(
		(term: string) => {
			return loadingStates[term] || { gettingDetail: false, adding: false };
		},
		[loadingStates]
	);

	const handleGetDetails = useCallback(async () => {
		// Details are already available from translation, no need to fetch again
		return;
	}, []);

	const handleAddToCollection = useCallback(async () => {
		// This feature is not applicable for vocabulary translate page
		// as words are automatically saved during translation
		showSuccess(t('vocabulary_translate.word_already_saved'));
	}, [showSuccess, t]);

	const handleUndoWordAddition = useCallback(async () => {
		// This feature is not applicable for vocabulary translate page
		return;
	}, []);

	const handleGenerateExamples = useCallback(async () => {
		// This feature is not applicable for vocabulary translate page
		return;
	}, []);

	const handleClear = useCallback(() => {
		setTranslatedWords([]);
	}, []);

	return (
		<main role="main" aria-label={t('accessibility.vocabulary_translate_page')}>
			<ScreenReaderAnnouncement
				message={t('accessibility.vocabulary_translate')}
				priority="polite"
			/>

			<div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
				<div className="container mx-auto px-4 py-8 space-y-8">
					{/* Header */}
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-4">
							<Link href="/">
								<Button variant="ghost" size="sm" className="gap-2">
									<ArrowLeft className="h-4 w-4" />
									<Translate text="ui.back" />
								</Button>
							</Link>
							<div className="flex items-center gap-2">
								<Languages className="h-6 w-6 text-primary" />
								<h1 className="text-2xl font-bold">
									<Translate text="vocabulary_translate.page_title" />
								</h1>
							</div>
						</div>
					</div>

					{/* Description */}
					<Card className="p-6 bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
						<p className="text-muted-foreground text-center">
							<Translate text="vocabulary_translate.page_description" />
						</p>
					</Card>

					{/* Input Form */}
					<VocabularyInputForm
						onTranslate={handleTranslate}
						isLoading={isTranslating}
						maxWords={10}
					/>

					{/* Results */}
					{translatedWords.length > 0 && (
						<div className="space-y-4">
							<div className="flex items-center justify-between">
								<h2 className="text-xl font-semibold">
									<Translate text="vocabulary_translate.results_title" />
								</h2>
								<Button
									onClick={handleClear}
									variant="outline"
									size="sm"
									className="rounded-xl"
								>
									<Translate text="ui.clear" />
								</Button>
							</div>

							<WordList
								words={translatedWords.map((word) => ({
									term: word.term,
									partOfSpeech: word.definitions[0]?.pos || [],
									meaning:
										word.definitions[0]?.explains?.map((explain) => ({
											EN: explain.EN,
											VI: explain.VI,
										})) || [],
								}))}
								detailedWords={translatedWords.reduce((acc, word) => {
									acc[word.term] = word;
									return acc;
								}, {} as Record<string, WordDetail>)}
								onGetDetails={handleGetDetails}
								getLoadingState={getLoadingState}
								onAddToCollection={handleAddToCollection}
								onUndoWordAddition={handleUndoWordAddition}
								onGenerateExamples={handleGenerateExamples}
								addedWords={new Set()}
								sourceLanguage={Language.EN}
								targetLanguage={Language.VI}
								className="mt-6"
							/>
						</div>
					)}
				</div>
			</div>
		</main>
	);
}
