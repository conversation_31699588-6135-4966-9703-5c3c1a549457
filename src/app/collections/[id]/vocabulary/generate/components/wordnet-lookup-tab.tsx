'use client';

import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON>nt,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Input,
	LoadingSpinner,
	Translate,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import { useTranslation } from '@/contexts';
import { WordNetEntry } from '@/backend/services/wordnet.service';
import { Search, X, RefreshCw, Network } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { WordNetCard } from './wordnet-card';
import { AllWordNetSection } from './all-wordnet-section';

interface WordNetLookupTabProps {
	collectionId: string;
}

export function WordNetLookupTab({ collectionId }: WordNetLookupTabProps) {
	const { t } = useTranslation();
	const { showError, showSuccess } = useToast();

	const [searchTerm, setSearchTerm] = useState('');
	const [searchResults, setSearchResults] = useState<WordNetEntry[]>([]);
	const [isSearching, setIsSearching] = useState(false);
	const [hasSearched, setHasSearched] = useState(false);
	const [recentSearches, setRecentSearches] = useState<string[]>([]);

	// Load recent searches from localStorage
	useEffect(() => {
		const saved = localStorage.getItem('vocab-recent-searches');
		if (saved) {
			try {
				setRecentSearches(JSON.parse(saved));
			} catch (error) {
				console.error('Failed to load recent searches:', error);
			}
		}
	}, []);

	// Save recent search
	const saveRecentSearch = useCallback((term: string) => {
		setRecentSearches((prev) => {
			const updated = [term, ...prev.filter((t) => t !== term)].slice(0, 10);
			localStorage.setItem('vocab-recent-searches', JSON.stringify(updated));
			return updated;
		});
	}, []);

	// Handle search
	const handleSearch = useCallback(async () => {
		if (!searchTerm.trim() || isSearching) return;

		setIsSearching(true);
		setHasSearched(true);

		try {
			const params = new URLSearchParams({
				term: searchTerm.trim(),
				limit: '20',
			});

			const response = await fetch(`/api/wordnet/search?${params}`);

			if (!response.ok) {
				throw new Error('Search failed');
			}

			const results = await response.json();
			setSearchResults(results);
			saveRecentSearch(searchTerm.trim());

			if (results.length === 0) {
				showSuccess(t('wordnet_lookup.no_results'));
			} else {
				console.log(`✅ Found ${results.length} WordNet entries`);
			}
		} catch (error) {
			console.error('Search error:', error);
			showError(t('wordnet_lookup.search_error'));
			setSearchResults([]);
		} finally {
			setIsSearching(false);
		}
	}, [searchTerm, t, showError, showSuccess, saveRecentSearch]);

	const handleClear = useCallback(() => {
		setSearchTerm('');
		setSearchResults([]);
		setHasSearched(false);
	}, []);

	const clearRecentSearches = useCallback(() => {
		setRecentSearches([]);
		localStorage.removeItem('vocab-recent-searches');
	}, []);

	const handleRecentSearchClick = useCallback((term: string) => {
		setSearchTerm(term);
	}, []);

	return (
		<div className="space-y-6">
			{/* Description */}
			<Card className="p-6 bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
				<p className="text-muted-foreground text-center">
					<Translate text="wordnet_lookup.subtitle" />
				</p>
			</Card>

			{/* Search Section */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Search className="h-5 w-5" />
						<Translate text="wordnet_lookup.search_button" />
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					{/* Search Input and Controls */}
					<div className="flex gap-4 items-end">
						<div className="flex-1">
							<Input
								type="text"
								placeholder={t('wordnet_lookup.search_placeholder')}
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
								className="text-lg"
							/>
						</div>
						<Button
							onClick={handleSearch}
							disabled={isSearching || !searchTerm.trim()}
							className="px-6"
						>
							{isSearching ? (
								<>
									<RefreshCw className="h-4 w-4 mr-2 animate-spin" />
									<Translate text="wordnet_lookup.searching" />
								</>
							) : (
								<>
									<Search className="h-4 w-4 mr-2" />
									<Translate text="wordnet_lookup.search_button" />
								</>
							)}
						</Button>
						{(searchTerm || hasSearched) && (
							<Button onClick={handleClear} variant="outline" size="icon">
								<X className="h-4 w-4" />
							</Button>
						)}
					</div>

					{/* Recent Searches */}
					{recentSearches.length > 0 && (
						<div className="space-y-2">
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium">
									<Translate text="wordnet_lookup.recent_searches" />
								</span>
								<Button
									onClick={clearRecentSearches}
									variant="ghost"
									size="sm"
									className="text-xs"
								>
									<Translate text="wordnet_lookup.clear_recent" />
								</Button>
							</div>
							<div className="flex flex-wrap gap-2">
								{recentSearches.map((term) => (
									<Button
										key={term}
										onClick={() => handleRecentSearchClick(term)}
										variant="outline"
										size="sm"
										className="text-xs"
									>
										{term}
									</Button>
								))}
							</div>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Search Results */}
			{hasSearched && (
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Network className="h-5 w-5" />
							<Translate text="wordnet_lookup.search_results" />
							{searchResults.length > 0 && (
								<span className="text-sm font-normal text-muted-foreground">
									({searchResults.length} results)
								</span>
							)}
						</CardTitle>
					</CardHeader>
					<CardContent>
						{isSearching ? (
							<div className="flex items-center justify-center py-8">
								<LoadingSpinner className="h-8 w-8" />
								<span className="ml-2">
									<Translate text="wordnet_lookup.loading" />
								</span>
							</div>
						) : searchResults.length > 0 ? (
							<div className="grid gap-4">
								{searchResults.map((entry) => (
									<WordNetCard key={`${entry.term}-${entry.pos}`} entry={entry} />
								))}
							</div>
						) : (
							<div className="text-center py-8 text-muted-foreground">
								<Translate text="wordnet_lookup.no_results" />
							</div>
						)}
					</CardContent>
				</Card>
			)}

			{/* All WordNet Entries Section */}
			<AllWordNetSection />
		</div>
	);
}
