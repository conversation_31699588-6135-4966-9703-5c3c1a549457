'use client';

import { VocabularyInputForm } from '@/components/vocabulary-translate/vocabulary-input-form';
import { WordList } from './word-list';
import { <PERSON><PERSON>, <PERSON>, ScreenReaderAnnouncement, Translate } from '@/components/ui';
import { useTranslation, useAuth, useGuidance } from '@/contexts';
import { useCollections } from '@/hooks';
import { useToast } from '@/contexts/toast-context';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { useCallback, useEffect, useState } from 'react';

interface LoadingState {
	[term: string]: {
		gettingDetail: boolean;
		adding: boolean;
		generatingExamples: boolean;
	};
}

interface VocabularyTranslateTabProps {
	collectionId: string;
}

export function VocabularyTranslateTab({ collectionId }: VocabularyTranslateTabProps) {
	const { t } = useTranslation();
	const { user } = useAuth();
	const { showError, showSuccess } = useToast();
	const { showGuidance } = useGuidance();
	const { currentCollection } = useCollections();
	const [translatedWords, setTranslatedWords] = useState<WordDetail[]>([]);
	const [isTranslating, setIsTranslating] = useState(false);
	const [loadingStates, setLoadingStates] = useState<LoadingState>({});
	const [addedWords, setAddedWords] = useState<Set<string>>(new Set());

	// Set up guidance for this tab
	useEffect(() => {
		showGuidance({
			titleKey: 'vocabulary_translate.guidance.title',
			steps: [
				{ key: 'vocabulary_translate.guidance.step1' },
				{ key: 'vocabulary_translate.guidance.step2' },
				{ key: 'vocabulary_translate.guidance.step3' },
			],
			tipKey: 'vocabulary_translate.guidance.tip',
			defaultOpen: false, // Don't auto-open in tab context
		});
	}, [showGuidance]);

	const handleTranslate = useCallback(
		async (words: string[]) => {
			if (!user || words.length === 0) return;

			setIsTranslating(true);
			try {
				const response = await fetch('/api/vocabulary-translate', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						words,
						source_language: currentCollection?.source_language || Language.EN,
						target_language: currentCollection?.target_language || Language.VI,
					}),
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(
						errorData.error || t('vocabulary_translate.translation_failed')
					);
				}

				const result: WordDetail[] = await response.json();
				setTranslatedWords(result);
				showSuccess(
					t('vocabulary_translate.translation_success', { count: result.length })
				);
			} catch (error) {
				const err = error instanceof Error ? error : new Error('Translation failed');
				showError(err);
			} finally {
				setIsTranslating(false);
			}
		},
		[user, currentCollection, t, showError, showSuccess]
	);

	const handleClear = useCallback(() => {
		setTranslatedWords([]);
	}, []);

	const getLoadingState = useCallback(
		(term: string) => {
			return (
				loadingStates[term] || {
					gettingDetail: false,
					adding: false,
					generatingExamples: false,
				}
			);
		},
		[loadingStates]
	);

	const handleGetDetails = useCallback(
		async (word: { term: string }) => {
			if (!currentCollection) return;

			// Set loading state
			setLoadingStates((prev) => ({
				...prev,
				[word.term]: { ...prev[word.term], gettingDetail: true },
			}));

			try {
				const response = await fetch('/api/llm/word-details', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						term: word.term,
						source_language: currentCollection.source_language,
						target_language: currentCollection.target_language,
					}),
				});

				if (!response.ok) {
					throw new Error('Failed to get word details');
				}

				const wordDetail: WordDetail = await response.json();

				// Update translated words with detailed information
				setTranslatedWords((prev) =>
					prev.map((w) => (w.term === word.term ? wordDetail : w))
				);
			} catch (error) {
				const err =
					error instanceof Error ? error : new Error('Failed to get word details');
				showError(err);
			} finally {
				// Clear loading state
				setLoadingStates((prev) => ({
					...prev,
					[word.term]: { ...prev[word.term], gettingDetail: false },
				}));
			}
		},
		[currentCollection, showError]
	);

	const handleAddToCollection = useCallback(
		async (word: { term: string }) => {
			if (!currentCollection) return;

			// Set loading state
			setLoadingStates((prev) => ({
				...prev,
				[word.term]: { ...prev[word.term], adding: true },
			}));

			try {
				const response = await fetch(`/api/collections/${currentCollection.id}/words`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						term: word.term,
					}),
				});

				if (!response.ok) {
					throw new Error('Failed to add word to collection');
				}

				setAddedWords((prev) => new Set([...prev, word.term]));
				showSuccess(t('words.word_added'));
			} catch (error) {
				const err = error instanceof Error ? error : new Error('Failed to add word');
				showError(err);
			} finally {
				// Clear loading state
				setLoadingStates((prev) => ({
					...prev,
					[word.term]: { ...prev[word.term], adding: false },
				}));
			}
		},
		[currentCollection, t, showError, showSuccess]
	);

	const handleUndoWordAddition = useCallback(
		async (word: { term: string }) => {
			if (!currentCollection) return;

			try {
				const response = await fetch(
					`/api/collections/${currentCollection.id}/words/${word.term}`,
					{
						method: 'DELETE',
					}
				);

				if (!response.ok) {
					throw new Error('Failed to remove word from collection');
				}

				setAddedWords((prev) => {
					const newSet = new Set(prev);
					newSet.delete(word.term);
					return newSet;
				});
				showSuccess(t('words.word_removed'));
			} catch (error) {
				const err = error instanceof Error ? error : new Error('Failed to remove word');
				showError(err);
			}
		},
		[currentCollection, t, showError, showSuccess]
	);

	const handleGenerateExamples = useCallback(
		async (word: { term: string }) => {
			if (!currentCollection) return;

			// Set loading state
			setLoadingStates((prev) => ({
				...prev,
				[word.term]: { ...prev[word.term], generatingExamples: true },
			}));

			try {
				const response = await fetch('/api/llm/generate-examples', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						term: word.term,
						source_language: currentCollection.source_language,
						target_language: currentCollection.target_language,
						count: 3,
					}),
				});

				if (!response.ok) {
					throw new Error('Failed to generate examples');
				}

				const { examples } = await response.json();

				// Update translated words with new examples
				setTranslatedWords((prev) =>
					prev.map((w) => {
						if (w.term === word.term && w.definitions?.[0]) {
							return {
								...w,
								definitions: [
									{
										...w.definitions[0],
										examples: examples,
									},
								],
							};
						}
						return w;
					})
				);

				showSuccess(t('words.examples_generated'));
			} catch (error) {
				const err =
					error instanceof Error ? error : new Error('Failed to generate examples');
				showError(err);
			} finally {
				// Clear loading state
				setLoadingStates((prev) => ({
					...prev,
					[word.term]: { ...prev[word.term], generatingExamples: false },
				}));
			}
		},
		[currentCollection, t, showError, showSuccess]
	);

	if (!currentCollection) return null;

	return (
		<div className="space-y-6">
			<ScreenReaderAnnouncement message={t('vocabulary_translate.page_title')} />

			{/* Description */}
			<Card className="p-6 bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
				<p className="text-muted-foreground text-center">
					<Translate text="vocabulary_translate.page_description" />
				</p>
			</Card>

			{/* Input Form */}
			<VocabularyInputForm
				onTranslate={handleTranslate}
				isLoading={isTranslating}
				maxWords={10}
			/>

			{/* Results */}
			{translatedWords.length > 0 && (
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<h2 className="text-xl font-semibold">
							<Translate text="vocabulary_translate.results_title" />
						</h2>
						<Button
							onClick={handleClear}
							variant="outline"
							size="sm"
							className="rounded-xl"
						>
							<Translate text="ui.clear" />
						</Button>
					</div>

					<WordList
						words={translatedWords.map((word) => ({
							term: word.term,
							partOfSpeech: word.definitions[0]?.pos || [],
							meaning:
								word.definitions[0]?.explains?.map((explain) => ({
									EN: explain.EN,
									VI: explain.VI,
								})) || [],
						}))}
						detailedWords={translatedWords.reduce((acc, word) => {
							acc[word.term] = word;
							return acc;
						}, {} as Record<string, WordDetail>)}
						onGetDetails={handleGetDetails}
						getLoadingState={getLoadingState}
						onAddToCollection={handleAddToCollection}
						onUndoWordAddition={handleUndoWordAddition}
						onGenerateExamples={handleGenerateExamples}
						addedWords={addedWords}
						sourceLanguage={currentCollection.source_language}
						targetLanguage={currentCollection.target_language}
						className="mt-6"
					/>
				</div>
			)}
		</div>
	);
}
