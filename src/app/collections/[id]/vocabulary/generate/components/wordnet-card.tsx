'use client';

import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Card<PERSON><PERSON><PERSON>,
	Translate,
} from '@/components/ui';
import { cn } from '@/lib';
import { WordNetEntry } from '@/backend/services/wordnet.service';
import { ChevronDown, ChevronUp, Volume2, Network } from 'lucide-react';
import { memo, useCallback, useState } from 'react';

interface WordNetCardProps {
	entry: WordNetEntry;
	className?: string;
}

export const WordNetCard = memo(function WordNetCard({ entry, className }: WordNetCardProps) {
	const [isExpanded, setIsExpanded] = useState(false);

	const toggleExpanded = useCallback(() => {
		setIsExpanded(!isExpanded);
	}, [isExpanded]);

	const handlePlayAudio = useCallback(() => {
		// Use Web Speech API for pronunciation
		if ('speechSynthesis' in window) {
			const utterance = new SpeechSynthesisUtterance(entry.term);
			utterance.lang = 'en-US';
			speechSynthesis.speak(utterance);
		}
	}, [entry.term]);

	// Check if we have any meaningful data
	const hasData = entry.synsets.length > 0 || 
					entry.hypernyms.length > 0 || 
					entry.hyponyms.length > 0 || 
					entry.holonyms.length > 0 || 
					entry.meronyms.length > 0;

	return (
		<Card className={cn('transition-all duration-200 hover:shadow-md', className)}>
			<CardHeader className="pb-3">
				<div className="flex items-start justify-between">
					<div className="flex-1">
						<CardTitle className="flex items-center gap-3 text-xl">
							<span className="font-bold">{entry.term}</span>
							<Badge variant="secondary" className="text-xs">
								{entry.pos.toUpperCase()}
							</Badge>
							<Badge variant="outline" className="text-xs">
								<Network className="w-3 h-3 mr-1" />
								WordNet
							</Badge>
						</CardTitle>
						{entry.lemma && entry.lemma !== entry.term && (
							<div className="flex items-center gap-2 mt-2">
								<span className="text-sm text-muted-foreground">
									Lemma: <span className="font-mono">{entry.lemma}</span>
								</span>
								<Button
									variant="ghost"
									size="sm"
									onClick={handlePlayAudio}
									className="h-6 w-6 p-0"
								>
									<Volume2 className="h-3 w-3" />
								</Button>
							</div>
						)}
					</div>
					<Button
						variant="ghost"
						size="sm"
						onClick={toggleExpanded}
						className="ml-2"
					>
						{isExpanded ? (
							<>
								<ChevronUp className="h-4 w-4 mr-1" />
								<Translate text="vocabulary_lookup.collapse_details" />
							</>
						) : (
							<>
								<ChevronDown className="h-4 w-4 mr-1" />
								<Translate text="vocabulary_lookup.expand_details" />
							</>
						)}
					</Button>
				</div>
			</CardHeader>

			<CardContent className="pt-0">
				{/* Synsets (Always Visible) */}
				{entry.synsets.length > 0 && (
					<div className="space-y-2">
						<h4 className="text-sm font-medium">
							<Translate text="wordnet.synsets" />
						</h4>
						<div className="space-y-1">
							{entry.synsets.slice(0, isExpanded ? undefined : 2).map((synset, index) => (
								<div key={index} className="text-sm p-3 rounded-lg bg-accent/25 border border-border/50">
									{synset}
								</div>
							))}
							{!isExpanded && entry.synsets.length > 2 && (
								<div className="text-xs text-muted-foreground">
									+{entry.synsets.length - 2} more synset{entry.synsets.length > 3 ? 's' : ''}
								</div>
							)}
						</div>
					</div>
				)}

				{/* Detailed Information (Only when expanded) */}
				{isExpanded && (
					<div className="mt-6 space-y-4">
						{/* Hypernyms */}
						{entry.hypernyms.length > 0 && (
							<div>
								<h4 className="text-sm font-medium mb-2">
									<Translate text="wordnet.hypernyms" />
								</h4>
								<div className="flex flex-wrap gap-1">
									{entry.hypernyms.map((hypernym, index) => (
										<Badge key={index} variant="outline" className="text-xs">
											{hypernym}
										</Badge>
									))}
								</div>
							</div>
						)}

						{/* Hyponyms */}
						{entry.hyponyms.length > 0 && (
							<div>
								<h4 className="text-sm font-medium mb-2">
									<Translate text="wordnet.hyponyms" />
								</h4>
								<div className="flex flex-wrap gap-1">
									{entry.hyponyms.map((hyponym, index) => (
										<Badge key={index} variant="outline" className="text-xs">
											{hyponym}
										</Badge>
									))}
								</div>
							</div>
						)}

						{/* Holonyms */}
						{entry.holonyms.length > 0 && (
							<div>
								<h4 className="text-sm font-medium mb-2">
									<Translate text="wordnet.holonyms" />
								</h4>
								<div className="flex flex-wrap gap-1">
									{entry.holonyms.map((holonym, index) => (
										<Badge key={index} variant="outline" className="text-xs">
											{holonym}
										</Badge>
									))}
								</div>
							</div>
						)}

						{/* Meronyms */}
						{entry.meronyms.length > 0 && (
							<div>
								<h4 className="text-sm font-medium mb-2">
									<Translate text="wordnet.meronyms" />
								</h4>
								<div className="flex flex-wrap gap-1">
									{entry.meronyms.map((meronym, index) => (
										<Badge key={index} variant="outline" className="text-xs">
											{meronym}
										</Badge>
									))}
								</div>
							</div>
						)}
					</div>
				)}

				{/* No Data Message */}
				{!hasData && (
					<div className="text-center py-4 text-muted-foreground">
						<Translate text="vocabulary_lookup.no_wordnet_data" />
					</div>
				)}
			</CardContent>
		</Card>
	);
});
